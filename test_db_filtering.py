#!/usr/bin/env python3
"""
Test script to demonstrate the dB filtering functionality in speech_to_text.py

This script creates synthetic audio samples at different volume levels
to test the dB detection and filtering functionality.
"""

import numpy as np
import logging
from speech_to_text import (
    _calculate_audio_db_level, 
    _preprocess_audio_numpy,
    set_db_threshold,
    get_db_threshold
)

# Set up logging to see the filtering in action
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_audio(amplitude, duration_ms=500, sample_rate=48000, channels=2):
    """
    Create synthetic audio data for testing.
    
    Args:
        amplitude (float): Amplitude level (0.0 to 1.0)
        duration_ms (int): Duration in milliseconds
        sample_rate (int): Sample rate in Hz
        channels (int): Number of audio channels
    
    Returns:
        bytes: Raw audio data as 16-bit PCM
    """
    # Calculate number of samples
    num_samples = int(duration_ms * sample_rate / 1000)
    
    # Generate a simple sine wave
    t = np.linspace(0, duration_ms / 1000, num_samples)
    frequency = 440  # A4 note
    mono_signal = amplitude * np.sin(2 * np.pi * frequency * t)
    
    # Convert to stereo
    if channels == 2:
        stereo_signal = np.column_stack([mono_signal, mono_signal])
    else:
        stereo_signal = mono_signal.reshape(-1, 1)
    
    # Convert to 16-bit integers
    int16_signal = (stereo_signal * 32767).astype(np.int16)
    
    # Convert to bytes
    return int16_signal.tobytes()

def test_db_calculation():
    """Test the dB calculation function with known values."""
    logger.info("=== Testing dB Calculation ===")
    
    # Test with different amplitude levels
    test_cases = [
        (1.0, "Full scale"),
        (0.5, "Half scale"),
        (0.1, "Low level"),
        (0.01, "Very low level"),
        (0.001, "Extremely low level"),
        (0.0, "Silent")
    ]
    
    for amplitude, description in test_cases:
        # Create normalized samples directly
        samples = np.full(1000, amplitude, dtype=np.float32)
        db_level = _calculate_audio_db_level(samples)
        
        if db_level == float('-inf'):
            logger.info(f"{description} (amplitude={amplitude}): -∞ dB")
        else:
            logger.info(f"{description} (amplitude={amplitude}): {db_level:.1f} dB")

def test_audio_filtering():
    """Test the audio filtering with different volume levels."""
    logger.info("\n=== Testing Audio Filtering ===")
    
    # Show current threshold
    current_threshold = get_db_threshold()
    logger.info(f"Current dB threshold: {current_threshold} dB")
    
    # Test with different amplitude levels
    test_levels = [
        (0.5, "Loud audio"),
        (0.1, "Normal speech"),
        (0.05, "Quiet speech"),
        (0.01, "Very quiet"),
        (0.005, "Background noise level"),
        (0.001, "Very low background noise")
    ]
    
    for amplitude, description in test_levels:
        logger.info(f"\n--- Testing {description} (amplitude={amplitude}) ---")
        
        # Create test audio
        audio_data = create_test_audio(amplitude)
        
        # Process through the preprocessing function
        result = _preprocess_audio_numpy(audio_data)
        
        if result.size == 0:
            logger.info(f"❌ {description}: FILTERED OUT")
        else:
            logger.info(f"✅ {description}: PASSED FILTER ({len(result)} samples)")

def test_threshold_adjustment():
    """Test adjusting the dB threshold."""
    logger.info("\n=== Testing Threshold Adjustment ===")
    
    # Create a test audio sample
    test_amplitude = 0.02  # This should be around -34 dB
    audio_data = create_test_audio(test_amplitude)
    
    # Test with different thresholds
    thresholds = [-50, -40, -30, -20]
    
    for threshold in thresholds:
        logger.info(f"\n--- Setting threshold to {threshold} dB ---")
        set_db_threshold(threshold)
        
        result = _preprocess_audio_numpy(audio_data)
        
        if result.size == 0:
            logger.info(f"Audio FILTERED OUT with {threshold} dB threshold")
        else:
            logger.info(f"Audio PASSED with {threshold} dB threshold")

def main():
    """Run all tests."""
    logger.info("Starting dB filtering tests...\n")
    
    try:
        test_db_calculation()
        test_audio_filtering()
        test_threshold_adjustment()
        
        logger.info("\n=== Test Summary ===")
        logger.info("✅ All tests completed successfully!")
        logger.info("The dB filtering is working correctly.")
        logger.info(f"Final threshold setting: {get_db_threshold()} dB")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)

if __name__ == "__main__":
    main()
