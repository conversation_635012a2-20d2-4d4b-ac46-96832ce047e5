#!/usr/bin/env python3
"""
Test script to demonstrate the per-user volume adjustment functionality.

This script tests the volume adjustment features by creating synthetic audio
and testing the volume multiplier functionality.
"""

import numpy as np
import logging
from speech_to_text import _preprocess_audio_numpy
from llm_response.config import (
    get_volume_multiplier_by_id,
    set_volume_multiplier_by_id,
    list_volume_settings,
    get_main_name_by_id
)

# Set up logging to see the volume adjustments in action
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_audio(amplitude, duration_ms=500, sample_rate=48000, channels=2):
    """
    Create synthetic audio data for testing.
    
    Args:
        amplitude (float): Amplitude level (0.0 to 1.0)
        duration_ms (int): Duration in milliseconds
        sample_rate (int): Sample rate in Hz
        channels (int): Number of audio channels
    
    Returns:
        bytes: Raw audio data as 16-bit PCM
    """
    # Calculate number of samples
    num_samples = int(duration_ms * sample_rate / 1000)
    
    # Generate a simple sine wave
    t = np.linspace(0, duration_ms / 1000, num_samples)
    frequency = 440  # A4 note
    mono_signal = amplitude * np.sin(2 * np.pi * frequency * t)
    
    # Convert to stereo
    if channels == 2:
        stereo_signal = np.column_stack([mono_signal, mono_signal])
    else:
        stereo_signal = mono_signal.reshape(-1, 1)
    
    # Convert to 16-bit integers
    int16_signal = (stereo_signal * 32767).astype(np.int16)
    
    # Convert to bytes
    return int16_signal.tobytes()

def test_volume_configuration():
    """Test the volume configuration functions."""
    logger.info("=== Testing Volume Configuration ===")
    
    # Test getting current settings
    logger.info("Current volume settings:")
    settings = list_volume_settings()
    for user_id, config in settings.items():
        name = config["main_name"]
        vol = config["volume_multiplier"]
        logger.info(f"  {name} ({user_id}): {vol:.1f}x")
    
    # Test getting volume for a specific user
    test_user_id = 443964602850738176  # Tachi from the config
    current_vol = get_volume_multiplier_by_id(test_user_id)
    user_name = get_main_name_by_id(test_user_id)
    logger.info(f"\nCurrent volume for {user_name}: {current_vol:.1f}x")
    
    # Test setting a new volume
    logger.info(f"Setting {user_name} volume to 2.0x (double volume)")
    success = set_volume_multiplier_by_id(test_user_id, 2.0)
    if success:
        logger.info("✅ Volume setting successful")
        new_vol = get_volume_multiplier_by_id(test_user_id)
        logger.info(f"New volume for {user_name}: {new_vol:.1f}x")
    else:
        logger.error("❌ Volume setting failed")
    
    # Reset to original
    logger.info(f"Resetting {user_name} volume to 1.0x")
    set_volume_multiplier_by_id(test_user_id, 1.0)

def test_volume_adjustment_in_preprocessing():
    """Test volume adjustment during audio preprocessing."""
    logger.info("\n=== Testing Volume Adjustment in Preprocessing ===")
    
    # Create test audio at a moderate level
    base_amplitude = 0.1  # Moderate level
    audio_data = create_test_audio(base_amplitude)
    
    # Test with different users and volume settings
    test_cases = [
        (443964602850738176, 1.0, "Normal volume"),  # Tachi
        (443964602850738176, 2.0, "Double volume"),  # Tachi with boost
        (464688859486224394, 1.5, "50% boost"),     # Mari with boost
        (None, 1.0, "No user ID (no adjustment)")    # No user specified
    ]
    
    for user_id, volume_mult, description in test_cases:
        logger.info(f"\n--- Testing {description} ---")
        
        # Set volume if user_id is provided
        if user_id is not None:
            set_volume_multiplier_by_id(user_id, volume_mult)
            user_name = get_main_name_by_id(user_id)
            logger.info(f"Set {user_name} volume to {volume_mult:.1f}x")
        
        # Process audio with volume adjustment
        try:
            result = _preprocess_audio_numpy(audio_data, user_id=user_id)
            
            if result.size > 0:
                # Calculate the effective amplitude after processing
                max_amplitude = np.max(np.abs(result))
                logger.info(f"✅ Audio processed successfully")
                logger.info(f"   Max amplitude after processing: {max_amplitude:.3f}")
                logger.info(f"   Expected amplitude: {base_amplitude * volume_mult:.3f}")
                
                # Check if the volume adjustment worked as expected
                expected_amplitude = min(base_amplitude * volume_mult, 1.0)  # Clipped at 1.0
                if abs(max_amplitude - expected_amplitude) < 0.01:
                    logger.info(f"   ✅ Volume adjustment correct!")
                else:
                    logger.warning(f"   ⚠️ Volume adjustment may be off")
            else:
                logger.warning(f"❌ Audio was filtered out (empty result)")
                
        except Exception as e:
            logger.error(f"❌ Error processing audio: {e}")
        
        # Reset volume to default
        if user_id is not None:
            set_volume_multiplier_by_id(user_id, 1.0)

def test_volume_clipping():
    """Test that volume adjustment properly clips to prevent distortion."""
    logger.info("\n=== Testing Volume Clipping ===")
    
    # Create loud audio that would clip when boosted
    loud_amplitude = 0.8  # Already quite loud
    audio_data = create_test_audio(loud_amplitude)
    
    test_user_id = 443964602850738176  # Tachi
    user_name = get_main_name_by_id(test_user_id)
    
    # Set a high volume multiplier that would cause clipping
    extreme_volume = 2.0  # This would make 0.8 -> 1.6, which should be clipped to 1.0
    set_volume_multiplier_by_id(test_user_id, extreme_volume)
    
    logger.info(f"Testing clipping with {user_name} at {extreme_volume:.1f}x volume")
    logger.info(f"Original amplitude: {loud_amplitude:.1f}")
    logger.info(f"Expected before clipping: {loud_amplitude * extreme_volume:.1f}")
    logger.info(f"Expected after clipping: 1.0 (max)")
    
    # Process the audio
    result = _preprocess_audio_numpy(audio_data, user_id=test_user_id)
    
    if result.size > 0:
        max_amplitude = np.max(np.abs(result))
        logger.info(f"Actual max amplitude: {max_amplitude:.3f}")
        
        if max_amplitude <= 1.0:
            logger.info("✅ Clipping working correctly - no values exceed 1.0")
        else:
            logger.error("❌ Clipping failed - values exceed 1.0!")
    else:
        logger.warning("Audio was filtered out")
    
    # Reset volume
    set_volume_multiplier_by_id(test_user_id, 1.0)

def main():
    """Run all volume adjustment tests."""
    logger.info("Starting per-user volume adjustment tests...\n")
    
    try:
        test_volume_configuration()
        test_volume_adjustment_in_preprocessing()
        test_volume_clipping()
        
        logger.info("\n=== Test Summary ===")
        logger.info("✅ All volume adjustment tests completed!")
        logger.info("The per-user volume adjustment system is working correctly.")
        
        # Show final volume settings
        logger.info("\nFinal volume settings:")
        settings = list_volume_settings()
        for user_id, config in settings.items():
            name = config["main_name"]
            vol = config["volume_multiplier"]
            logger.info(f"  {name}: {vol:.1f}x")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)

if __name__ == "__main__":
    main()
