import discord
import asyncio
import io
import time
import logging
import sqlite3
import re
import threading  # Add threading import for thread ID logging
from collections import defaultdict
from rag_retriever import RAGRetriever
from speech_to_text import transcribe_audio
# Import necessary components from llm_response
import llm_response # Import the module to access model/functions
# Import the latency logging helper
from llm_response.processing import log_latency_to_discord
from llm_brain import get_brain_decision # Keep for now if used elsewhere, but add main processor
import llm_brain # Import the module itself to call the new processor function
from llm_response.config import get_main_name_from_alias, get_main_name_by_id, get_id_from_alias # Import alias helpers
import os

# Define the transcript log channel ID
TRANSCRIPT_LOG_CHANNEL_ID = 1369158552996024370

logger = logging.getLogger(__name__)

class DiscordSink(discord.sinks.Sink):
    def __init__(self, text_channel, bot, voice_client, lm_studio_client): # Changed parameter name
        super().__init__()
        self.text_channel = text_channel
        self.bot = bot
        self.voice_client = voice_client # Store the voice client instance
        self.user_audio = {}
        self.last_packet_time = {}
        self.audio_start_time = {}  # DEPRECATED - Use audio_detected_monotonic_time
        self.audio_detected_monotonic_time = {} # Track monotonic time when audio first detected for a user
        self.play_lock = asyncio.Lock()
        self.buffer = io.BytesIO()
        self.bg_task = None
        self.conversation_history = [] # Overall persistent history
        self.max_history_length = 150
        self.last_history_timestamp = time.time()
        self.system_prompt = ""
        self.is_speaking = False
        self.pending_transcriptions = {} # Tracks audio being actively transcribed
        self.last_transcription_time = 0
        self.transcription_cooldown = 0.05 # Cooldown between transcriptions for a user
        self.is_processing_screenshot = False
        self.last_screenshot_time = 0
        self.screenshot_cooldown = 5 # Cooldown for screenshot command
        self.participation_counter = defaultdict(int) # Track user participation
        self.lm_studio_client = lm_studio_client # Store the passed LM Studio client
        self.chat_session = None # Initialize chat session attribute

        # --- State for Turn-Based Processing ---
        self.current_turn_transcripts = [] # Holds transcripts {"text":..., "user_id":..., ...} for the current turn
        self.last_activity_time = time.monotonic() # Timestamp of last audio packet or added transcript
        self.turn_processing_lock = asyncio.Lock() # Prevent race conditions processing turns
        self.current_turn_start_monotonic_time: float | None = None # Monotonic time when the turn processing started (for _after_play)
        # --- End Turn-Based State ---

        # Store the DB path instead of the connection
        self.db_path = "conversation_logs.db"

        # --- Database Initialization (Main Thread) ---
        # Connect briefly just to initialize/check schema
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("PRAGMA table_info(interactions)")
            existing_columns = {row[1] for row in cursor.fetchall()}
            required_columns = {
                'user_id', 'role', 'content', 'timestamp',
                'audio_start_time', 'channel_type', 'channel_id'
            }

            # Add missing columns
            for column in required_columns - existing_columns:
                if column == 'audio_start_time':
                    conn.execute("ALTER TABLE interactions ADD COLUMN audio_start_time REAL")
                elif column == 'channel_type':
                    conn.execute("ALTER TABLE interactions ADD COLUMN channel_type TEXT")
                elif column == 'channel_id':
                    conn.execute("ALTER TABLE interactions ADD COLUMN channel_id TEXT")

            # Create the table if it doesn't exist
            conn.execute('''CREATE TABLE IF NOT EXISTS interactions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id TEXT,
                            role TEXT,
                            content TEXT,
                            timestamp REAL,
                            audio_start_time REAL,
                            channel_type TEXT,
                            channel_id TEXT)''')
            conn.commit()
            conn.close()  # Close the connection immediately
            logger.info("Database schema checked/initialized.")
        except Exception as e:
            logger.error(f"Database initialization error: {e}")

        # Load conversation history (once at startup)
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("SELECT role, content, timestamp, user_id FROM interactions ORDER BY timestamp ASC")
            self.conversation_history = [{"role": row[0], "content": row[1], "timestamp": row[2], "user_id": row[3]} for row in cursor.fetchall()]
            conn.close()
            logger.info("Loaded %d historical interactions.", len(self.conversation_history))
        except Exception as e:
            logger.error("Error loading conversation history: %s", e)
            self.conversation_history = []  # Ensure it's an empty list on error

        # Add the system_prompt attribute that will be used by llm_response
        try:
            with open('system_prompt.txt', 'r') as f:
                self.system_prompt = f.read()
        except Exception as e:
            logger.error(f"Error loading system prompt: {e}")
            self.system_prompt = ""

        # --- Initialize Ollama Chat Session ---
        if self.lm_studio_client:
            try:
                logger.info("Starting Ollama Chat Session...")
                # Initialize session with system prompt
                session_id = f"voice_channel_{int(time.time())}"

                # Create a session object with the system prompt
                self.chat_session = {
                    "id": session_id,
                    "messages": []
                }

                # Add system prompt if available
                if self.system_prompt:
                    self.chat_session["messages"].append({"role": "system", "content": self.system_prompt})
                    logger.info(f"Added system prompt to chat session. Session now has {len(self.chat_session['messages'])} messages.")

                # Store the session in the bot's chat_sessions dictionary for global access
                voice_session_key = f"voice_{self.voice_client.channel.id}" if self.voice_client and self.voice_client.channel else "voice_unknown"
                self.bot.chat_sessions[voice_session_key] = self.chat_session
                logger.info(f"Stored voice chat session in bot.chat_sessions with key '{voice_session_key}'")

                logger.info("Ollama Chat Session initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize Ollama Chat Session: {e}", exc_info=True)
                self.chat_session = None # Ensure it's None on failure
        else:
            logger.error("LM Studio client not provided to DiscordSink, cannot initialize chat session.")

    def write(self, data, user_id):
        """This method is called by discord.py when audio data is received."""
        current_monotonic = time.monotonic() # Use monotonic time for activity tracking
        current_time = time.time() # Use wall time for audio start timestamp

        # Initialize user audio buffer and start time if first packet
        if user_id not in self.user_audio:
            self.user_audio[user_id] = bytearray()
            # self.audio_start_time[user_id] = current_time # Store start time (DEPRECATED)
            self.audio_detected_monotonic_time[user_id] = current_monotonic # Store monotonic time
            logger.debug(f"New speaker {user_id} detected at monotonic time {current_monotonic:.4f}")
            # REMOVED: Cannot call asyncio.create_task from this synchronous thread context

        # Append data and update timestamps
        self.user_audio[user_id].extend(data)
        self.last_packet_time[user_id] = current_monotonic # Update user's last packet time using monotonic
        self.last_activity_time = current_monotonic # Update overall last activity time

    async def _stream_to_buffer(self, response):
        try:
            for chunk in response.iter_bytes():
                self.buffer.write(chunk)
        except Exception as e:
            logger.error(f"Error in stream_to_buffer: {e}")

    def _run_db_write_sync(self, db_path, query, params):
        """Synchronous function to run DB writes with its own connection."""
        conn = None  # Initialize conn
        try:
            # Create a new connection specifically for this thread
            conn = sqlite3.connect(db_path, timeout=10)  # Added timeout
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            logger.debug(f"Successfully wrote to DB in thread {threading.get_ident()}")
            return True
        except sqlite3.OperationalError as e:
            logger.warning(f"Database insert error: {e}")
            # Fallback logic (ensure conn exists)
            try:
                if conn:
                    fallback_query = "INSERT INTO interactions (user_id, role, content, timestamp) VALUES (?, ?, ?, ?)"
                    fallback_params = (params[0], params[1], params[2], params[3])
                    cursor = conn.cursor()
                    cursor.execute(fallback_query, fallback_params)
                    conn.commit()
                    logger.debug(f"Successfully wrote fallback to DB in thread {threading.get_ident()}")
                    return True
                else:
                    logger.error("Cannot perform fallback DB write without connection.")
                    return False
            except Exception as e2:
                logger.error(f"Fallback database insert error: {e2}")
                return False
        except Exception as e_gen:
            logger.error(f"General database write error: {e_gen}")
            return False
        finally:
            # Ensure the connection is closed if it was opened
            if conn:
                conn.close()
                logger.debug(f"Closed DB connection in thread {threading.get_ident()}")

    async def _add_to_history(self, role, content, user_id="unknown", audio_timestamp=None, channel_type="voice", channel_id=None):
        """Add message to conversation history with asynchronous database operations."""
        now = time.time()
        segmentation_threshold = 300
        if now - self.last_history_timestamp > segmentation_threshold:
            self.conversation_history = []
        self.last_history_timestamp = now

        # Use the audio start timestamp if provided, otherwise use current time
        timestamp = audio_timestamp or now

        # Create the message entry with timestamp and channel info
        message_entry = {
            "role": role,
            "content": content,
            "timestamp": timestamp,
            "user_id": user_id,
            "channel_type": channel_type,
            "channel_id": channel_id
        }

        # --- Update in-memory conversation history (fast operation) ---
        if len(self.conversation_history) > 0 and timestamp < self.conversation_history[-1]["timestamp"]:
            # Find the right position to insert it
            insert_pos = len(self.conversation_history)
            for i, msg in enumerate(reversed(self.conversation_history)):
                if timestamp >= msg["timestamp"]:
                    insert_pos = len(self.conversation_history) - i
                    break
                if i == len(self.conversation_history) - 1:
                    insert_pos = 0

            self.conversation_history.insert(insert_pos, message_entry)
            logger.info(f"Inserted message from {user_id} at position {insert_pos} (out of time order)")
        else:
            # Normal case - append to history
            self.conversation_history.append(message_entry)

        # Trim if too long
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history.pop(0)

        # --- Perform database write asynchronously ---
        db_query = """
            INSERT INTO interactions
            (user_id, role, content, timestamp, audio_start_time, channel_type, channel_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        db_params = (user_id, role, content, now, timestamp, channel_type, channel_id)

        # Run the synchronous DB write function in a separate thread
        await asyncio.to_thread(
            self._run_db_write_sync,
            self.db_path,  # Pass the path instead of the connection
            db_query,
            db_params
        )

    def _after_play(self, error, user_id): # user_id here might be the *last* speaker, not necessarily the one who triggered the response
        playback_end_time = time.monotonic()
        self.is_speaking = False
        # Log total duration if start time is available
        if self.current_turn_start_monotonic_time:
            total_duration = playback_end_time - self.current_turn_start_monotonic_time
            # Use create_task as this isn't an async function
            asyncio.create_task(log_latency_to_discord(self.bot, f"Total Turn Processing -> Playback End: {total_duration:.4f}s"))
            self.current_turn_start_monotonic_time = None # Reset for next turn
        if error:
            logger.error(f"Playback error: {error}")
        if user_id in self.user_audio:
            self.user_audio[user_id].clear()
        if user_id in self.last_packet_time:
            del self.last_packet_time[user_id]
        if user_id in self.audio_start_time:
            del self.audio_start_time[user_id]

    def _quick_trigger_check(self, text):
        text_lower = text.lower()
        direct_triggers = {'luna', 'bot', 'assistant', 'ai', 'hey you', 'robot', '@luna'}
        if any(trigger in text_lower for trigger in direct_triggers):
            return True
        if '?' in text:
            return True
        cues = {'what do you think', 'opinion', 'suggest', 'idea', 'what about'}
        if any(cue in text_lower for cue in cues):
            return True
        mention_patterns = {
            r'\byou\b.*\bthink\b',
            r'\bhow about\b.*\byou\b',
            r'\bwhat\'?s your\b'
        }
        for pattern in mention_patterns:
            if re.search(pattern, text_lower):
                return True
        return False

    async def check_for_silence(self):
        while True:
            await asyncio.sleep(0.05) # Reduced interval from 0.1 for faster checking
            current_time = time.time() # Use wall time for silence check comparison

            for user_id, audio_bytes in list(self.user_audio.items()):
                if audio_bytes:
                    # Use monotonic time for time_since_last calculation
                    last_packet_mono = self.last_packet_time.get(user_id, time.monotonic())
                    time_since_last = time.monotonic() - last_packet_mono

                    # Reduce silence threshold to trigger transcription faster
                    if time_since_last > 0.4: # Reduced from 0.8
                        # Check transcription cooldown using wall time
                        if time.time() - self.last_transcription_time < self.transcription_cooldown:
                            continue

                        # Get the audio start time for proper ordering (wall time)
                        audio_start_time = self.audio_start_time.get(user_id, current_time)

                        # --- Start preparing transcription task ---
                        # Moved user fetching inside _process_transcription to avoid blocking this loop
                        try:
                            self.last_transcription_time = time.time() # Update cooldown timer

                            # Add minimal info needed to pending transcriptions
                            self.pending_transcriptions[user_id] = {
                                "start_time": audio_start_time, # Wall time
                                "audio_bytes": bytes(audio_bytes), # Create bytes copy
                                "audio_detected_time": self.audio_detected_monotonic_time.get(user_id) # Monotonic time
                            }

                            # Start the transcription asynchronously
                            pre_create_task_time = time.monotonic()
                            logger.info(f"DIAG: Creating task _process_transcription for user {user_id} at {pre_create_task_time:.4f}")
                            asyncio.create_task(self._process_transcription(user_id))

                        except Exception as task_prep_e:
                             # Catch errors specifically related to preparing/starting the task
                             logger.error(f"Error preparing transcription task for {user_id}: {task_prep_e}", exc_info=True)
                        finally:
                             # Always clear audio buffer *after* attempting to create the task
                             if user_id in self.user_audio:
                                 self.user_audio[user_id].clear()
                             if user_id in self.last_packet_time:
                                 del self.last_packet_time[user_id]
                             # Clean up monotonic time tracker as well
                             if user_id in self.audio_detected_monotonic_time:
                                 del self.audio_detected_monotonic_time[user_id]
                        # --- End preparing transcription task ---

                        # Removed the except blocks for fetch errors and the general 'Error preparing task'
                        # as the fetch logic is moved and the task prep error is handled above.
                        # The finally block for clearing audio is now inside the inner try/except/finally.
            # End of per-user silence check loop

            # --- Check for Group Silence ---
            # Define how long the group needs to be silent before processing the turn
            GROUP_SILENCE_THRESHOLD = 0.4 # seconds

            # Check if enough time has passed since the last activity (packet or transcript added)
            # AND if there are actually transcripts collected in the current turn
            current_monotonic_time = time.monotonic()
            # Check if enough time has passed AND if there are actually transcripts collected
            if self.current_turn_transcripts and (current_monotonic_time - self.last_activity_time > GROUP_SILENCE_THRESHOLD):

                # --- Critical Fix: Reset timer *immediately* upon detection ---
                # This prevents the condition from being met repeatedly before the task runs.
                self.last_activity_time = current_monotonic_time
                logger.info(f"Group silence detected (>{GROUP_SILENCE_THRESHOLD}s). Resetting activity timer and scheduling turn processing.")

                # Schedule the processing task
                asyncio.create_task(self._process_collected_turn())
            # --- End Group Silence Check ---

    async def _process_transcription(self, user_id):
        """Transcribes audio for a user and adds it to the current turn's transcripts."""
        process_start_time = time.monotonic() # DIAGNOSTIC TIMING
        logger.info(f"DIAG: _process_transcription called for user {user_id} at {process_start_time:.4f}") # DIAGNOSTIC TIMING

        # Ensure we still have pending data (might have been cleared by a rapid stop/start)
        if user_id not in self.pending_transcriptions:
            logger.warning(f"_process_transcription called for {user_id} but no pending data found.")
            return

        pending = self.pending_transcriptions[user_id]
        audio_bytes = pending["audio_bytes"]
        audio_start_time = pending["start_time"] # Wall time
        audio_detected_time = pending.get("audio_detected_time") # Monotonic time

        # --- Fetch user info and determine names (Moved here) ---
        display_name = f"User_{user_id}" # Default fallback
        # First try to get the canonical name directly from the user ID
        canonical_name = get_main_name_by_id(user_id)

        # If we couldn't get a canonical name from the ID, try to fetch the user and resolve from display name
        if not canonical_name:
            try:
                user = await self.bot.fetch_user(user_id)
                if self.text_channel and hasattr(self.text_channel, 'guild'):
                    guild = self.text_channel.guild
                    try:
                        member = await guild.fetch_member(user_id)
                        if member:
                            display_name = member.nick or member.name # Use nick first, then name
                            canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias
                    except discord.NotFound:
                        logger.debug(f"Member {user_id} not found in guild {guild.id}, using fetched user name.")
                        display_name = user.name
                        canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias
                    except discord.Forbidden:
                        logger.warning(f"Lacking permissions to fetch member {user_id} in guild {guild.id}.")
                        display_name = user.name # Fallback to fetched user name
                        canonical_name = get_main_name_from_alias(display_name)
                    except Exception as fetch_mem_e:
                        logger.error(f"Error fetching member {user_id}: {fetch_mem_e}")
                        display_name = user.name # Fallback to fetched user name
                        canonical_name = get_main_name_from_alias(display_name)
                else:
                    logger.debug(f"No guild context available, using fetched user name for {user_id}.")
                    display_name = user.name
                    canonical_name = get_main_name_from_alias(display_name) # Resolve potential alias

            except discord.NotFound:
                logger.warning(f"Could not fetch user {user_id} globally (NotFound). Using fallback name '{display_name}'.")
                # Keep fallback names assigned above
            except Exception as fetch_user_e:
                logger.error(f"Error fetching user {user_id}: {fetch_user_e}", exc_info=True)
                # Keep fallback names assigned above

        # If we still don't have a canonical name, use the display name
        if not canonical_name:
            canonical_name = display_name

        # --- Proceed with Transcription ---
        try:
            # Transcribe the audio using the determined display_name for logging
            logger.info(f"Starting transcription for {display_name}")
            pre_transcribe_call_time = time.monotonic() # DIAGNOSTIC TIMING
            logger.info(f"DIAG: Calling transcribe_audio at {pre_transcribe_call_time:.4f}") # DIAGNOSTIC TIMING
            transcribe_start_time = time.monotonic()
            text = await transcribe_audio(audio_bytes, user_id=user_id)  # Pass user_id for volume adjustment
            transcription_end_time = time.monotonic() # Capture end time
            transcription_duration = transcription_end_time - transcribe_start_time
            await log_latency_to_discord(self.bot, f"Transcription Duration ({canonical_name}): {transcription_duration:.4f}s") # Log with canonical name

            if not text:
                logger.info(f"Transcription for {canonical_name} resulted in empty text.") # Log with canonical name
                # Still update activity time even if transcription is empty, as processing happened
                self.last_activity_time = time.monotonic()
                # Clean up handled in finally block now
                return

            logger.info(f"Transcription for {canonical_name} (started at {audio_start_time:.2f}): {text}") # Log with canonical name

            # --- Add to current turn ---
            luna_mentioned = "luna" in text.lower()
            # audio_detected_time is now retrieved from the pending dictionary at the start

            transcript_entry = {
                "user_id": user_id,
                "original_display_name": display_name, # Store the name used for logging/display
                "canonical_name": canonical_name, # Store the resolved canonical name for history/context
                "text": text,
                "timestamp": audio_start_time, # Wall time for ordering
                "mentioned_luna": luna_mentioned,
                # Add latency timestamps
                "audio_detected_time": audio_detected_time, # Monotonic
                "transcription_end_time": transcription_end_time # Monotonic
            }

            # Append to the shared turn list
            self.current_turn_transcripts.append(transcript_entry)

            # Update last activity time since a transcript was added
            self.last_activity_time = time.monotonic()
            logger.debug(f"Added transcript from {canonical_name} to turn. Current turn size: {len(self.current_turn_transcripts)}") # Log with canonical name

            # --- Immediate Trigger if Luna Mentioned ---
            if luna_mentioned:
                logger.info(f"Luna mentioned by {canonical_name}, triggering immediate turn processing.") # Log with canonical name
                # Use create_task to avoid blocking the current transcription processing
                # We don't await this; it runs concurrently
                # Pass the specific transcript entry that triggered the mention
                asyncio.create_task(self._process_collected_turn(triggered_by_mention=transcript_entry))

            # --- Log transcription to the dedicated channel ---
            try:
                log_channel = self.bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                if log_channel:
                    log_message = f"**{canonical_name}:** {text}" # Log with canonical name
                    # Use create_task to avoid blocking transcription processing
                    asyncio.create_task(log_channel.send(log_message))
                else:
                    logger.warning(f"Transcript log channel {TRANSCRIPT_LOG_CHANNEL_ID} not found.")
            except Exception as log_err:
                logger.error(f"Error sending transcription to log channel: {log_err}", exc_info=True)

        # End of try block
        except Exception as e: # Ensure this aligns with try
            logger.error(f"Error during transcription processing for {user_id}: {e}", exc_info=True)
        finally: # Ensure this aligns with try
            # Clean up this specific pending transcription regardless of outcome
            # Ensure key exists before deleting, although initial check should cover this
            if user_id in self.pending_transcriptions:
                del self.pending_transcriptions[user_id]

    # Note: _respond_to_luna is likely no longer needed with the new logic,
    # but keep it for now in case handle_screenshot_command still uses it indirectly.
    # We might need to refactor screenshot handling later.
    async def _respond_to_luna(self, text, display_name, user_id):
        # This function might need refactoring or removal later
        logger.warning(f"_respond_to_luna called directly for {display_name} - This might be deprecated logic.")
        try:
            # Call process_user_message using keyword arguments
            # This likely needs adjustment for turn-based context
            await llm_response.process_user_message( # Add module prefix
                bot=self.bot,
                text=text, # This only passes the single utterance, not the turn context
                user_id=user_id,
                conversation_history=self.conversation_history, # Uses overall history, not turn context
                system_prompt=self.system_prompt,
                sink=self,
                force_respond=True,
                display_name=display_name,
                text_channel=self.text_channel
            )
        except Exception as e:
            logger.error(f"Error in _respond_to_luna: {e}", exc_info=True)
            if not self.is_speaking and self.bot.voice_clients:
                await self.text_channel.send("Sorry, I'm having trouble responding right now.")

    async def process_text_message(self, message):
        # This function needs review - should text messages contribute to the voice turn?
        # For now, keep it separate. It calls process_user_message directly.
        try:
            # Skip messages from bots (except our own responses)
            if message.author.bot and message.author.id != self.bot.user.id:
                return

            # Extract content and metadata
            content = message.content
            author_id = str(message.author.id)
            author_name = message.author.display_name
            timestamp = message.created_at.timestamp()
            # Handle different channel types (DMChannel doesn't have a name attribute)
            channel_name = message.channel.name if hasattr(message.channel, 'name') else "DM"

            # Check for Luna mentions
            luna_mentioned = any([
                self.bot.user.mentioned_in(message),
                "luna" in content.lower(),
                re.search(r'@luna\b', content, re.IGNORECASE)
            ])

            # Add to conversation history (same format as voice)
            await self._add_to_history(
                "user",
                f"{author_name}: {content}",
                author_id,
                timestamp,
                channel_type="text",
                channel_id=str(message.channel.id)
            )

            # Rate limit responses in the channel to avoid overactivity
            current_time = time.time()
            recent_responses = [msg for msg in self.conversation_history[-10:]
                              if msg.get("role") == "assistant"
                              and msg.get("channel_id") == str(message.channel.id)
                              and current_time - msg.get("timestamp", 0) < 60]  # Last minute

            # If we've been very active in this channel and not directly mentioned, be more selective
            if len(recent_responses) >= 3 and not luna_mentioned:
                # Only respond to very clear triggers when we've been active
                if "?" not in content or not any(word in content.lower() for word in ["you", "your"]):
                    # Handle different channel types for logging
                    channel_display = message.channel.name if hasattr(message.channel, 'name') else "DM"
                    logger.info(f"Skipping response due to high activity in channel: {channel_display}")
                    return

            if luna_mentioned:
                # Call process_user_message directly for text messages mentioning Luna
                await llm_response.process_user_message( # Add module prefix
                    bot=self.bot,
                    text=content,
                    user_id=author_id,
                    conversation_history=self.conversation_history,
                    system_prompt=self.system_prompt,
                    sink=self,
                    force_respond=True, # Force response if mentioned
                    display_name=author_name,
                    text_channel=message.channel
                )
        except Exception as e:
            logger.error(f"Error processing text message: {e}", exc_info=True)

    async def _execute_dm_action(self, brain_action_details):
        """Execute a DM action for voice commands."""
        try:
            target_identifier = brain_action_details.get('target')
            topic = brain_action_details.get('topic')

            if not target_identifier or not topic:
                logger.error(f"Missing DM target or topic: target='{target_identifier}', topic='{topic}'")
                return False

            logger.info(f"Executing DM action: target='{target_identifier}', topic='{topic}'")

            # Import necessary functions
            from llm_response import get_ollama_client, log_message_to_db
            from llm_response.config import OLLAMA_MODEL_NAME, DM_TEMP, DM_MAX_OUTPUT_TOKENS
            from utils import find_user_from_identifier
            import asyncio
            import time

            # Get Ollama client
            ollama_client = get_ollama_client()
            logger.info(f"DEBUG: Ollama client for voice DM: {ollama_client}")

            if not ollama_client:
                logger.error("Cannot generate DM content: Ollama client not available.")
                if self.text_channel:
                    await self.text_channel.send("❌ Cannot generate DM content: Ollama client not available.")
                return False

            # Find the target user
            guild_context = self.voice_client.guild if self.voice_client else None
            target_user = await find_user_from_identifier(
                identifier=target_identifier,
                bot=self.bot,
                guild=guild_context
            )

            if not target_user:
                logger.warning(f"Could not find user matching '{target_identifier}' to send DM.")
                if self.text_channel:
                    await self.text_channel.send(f"❓ Brain wanted to DM '{target_identifier}', but I couldn't find them.")
                return False

            # Generate DM content using Ollama
            logger.info(f"Generating DM content for target '{target_identifier}' about '{topic}'...")

            # Get or create DM chat session for statefulness
            dm_session_key = f"dm_{target_user.id}"
            dm_session = self.bot.chat_sessions.get(dm_session_key)

            if dm_session is None:
                logger.info(f"Creating new DM chat session for user {target_user.id}")
                dm_session = {
                    "id": f"dm_{target_user.id}_{int(time.time())}",
                    "messages": [{"role": "system", "content": self.system_prompt}]
                }
                self.bot.chat_sessions[dm_session_key] = dm_session

            # Simplified prompt for DM generation
            simple_dm_prompt = f"You need to send a DM about: '{topic}'. Generate a short, casual message:"

            # Use the DM session messages and add the new prompt
            messages = dm_session["messages"].copy()
            messages.append({"role": "user", "content": simple_dm_prompt})

            # Make the API call using Ollama
            dm_response = await asyncio.to_thread(
                ollama_client.chat.completions.create,
                model=OLLAMA_MODEL_NAME,
                messages=messages,
                temperature=DM_TEMP,
                max_tokens=DM_MAX_OUTPUT_TOKENS
            )

            if not dm_response.choices or dm_response.choices[0].finish_reason != "stop":
                finish_reason = dm_response.choices[0].finish_reason if dm_response.choices else "Unknown"
                logger.warning(f"LLM DM content generation failed or was blocked. Finish Reason: {finish_reason}")
                if self.text_channel:
                    await self.text_channel.send(f"⚠️ Tried to DM '{target_identifier}' about '{topic}', but couldn't generate content (Reason: {finish_reason}).")
                return False

            dm_content = dm_response.choices[0].message.content.strip()
            if not dm_content:
                logger.warning(f"LLM generated empty DM content for topic: {topic}")
                if self.text_channel:
                    await self.text_channel.send(f"⚠️ Tried to DM '{target_identifier}' about '{topic}', but couldn't think of what to say!")
                return False

            # Send the DM
            try:
                # Update DM session with user prompt and assistant response
                dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                dm_session["messages"].append({"role": "assistant", "content": dm_content})

                await target_user.send(dm_content)
                logger.info(f"Successfully sent generated DM to {target_user.name} ({target_user.id})")

                # Log to database
                log_message_to_db(
                    user_id=str(self.bot.user.id), role="assistant_dm",
                    content=f"Sent DM to {target_user.name} ({target_user.id}) about '{topic}': {dm_content}",
                    timestamp=time.time(), channel_type="dm", channel_id=str(target_user.id)
                )

                return True

            except Exception as send_err:
                logger.error(f"Failed to send DM to {target_user.name}: {send_err}")
                if self.text_channel:
                    await self.text_channel.send(f"❌ Failed to send DM to {target_user.mention}: {send_err}")
                return False

        except Exception as e:
            logger.error(f"Error executing DM action: {e}", exc_info=True)
            if self.text_channel:
                await self.text_channel.send(f"❌ An unexpected error occurred while trying to process the DM.")
            return False

    # Removed redundant _generate_and_send_text_response function.
    # Its logic is handled by llm_response.process_user_message.
    def get_recent_speaker_turns(self, count=5) -> list:
        """Gets the last 'count' unique speaker turns from history."""
        turns = []
        speakers_seen = set()
        # Iterate backwards through history
        for msg in reversed(self.conversation_history):
            user_id = msg.get("user_id")
            # Only consider user messages
            if msg.get("role") == "user" and user_id:
                if user_id not in speakers_seen:
                    # Get the canonical name for this user ID
                    # Convert user_id to int if it's a string, or use it directly if it's already an int
                    user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                    canonical_name = get_main_name_by_id(user_id_int)

                    # If we couldn't get a canonical name from the ID, try to extract from the message content
                    if not canonical_name:
                        # Try to extract name from content (format is typically "Name: message")
                        content = msg.get("content", "")
                        name_match = re.match(r"^([^:]+):", content)
                        if name_match:
                            extracted_name = name_match.group(1).strip()
                            # Use the extracted name or resolve it to a canonical name
                            canonical_name = get_main_name_from_alias(extracted_name)

                    # If we still don't have a name, use a fallback
                    display_name = canonical_name or f"User_{user_id}"

                    # Extract the actual message text (remove the name prefix if present)
                    content = msg.get("content", "")
                    text = content
                    text_match = re.match(r"^[^:]+:\s*(.*)", content)
                    if text_match:
                        text = text_match.group(1)

                    turns.append({"user_id": user_id, "display_name": display_name, "text": text})
                    speakers_seen.add(user_id)
                    if len(turns) >= count:
                        break
        return list(reversed(turns)) # Return in chronological order

    async def _process_collected_turn(self, triggered_by_mention: dict | None = None):
        """Processes the accumulated transcripts for the completed conversational turn."""
        # Use a lock to prevent multiple concurrent processing attempts for the same turn
        async with self.turn_processing_lock:
            # Double-check if transcripts are present (might have been processed by a rapid Luna mention)
            if not self.current_turn_transcripts and not triggered_by_mention:
                logger.debug("_process_collected_turn called but no transcripts found, likely already processed.")
                return

            # --- Prepare Turn Data (Handles Mention Trigger vs Group Silence) ---
            transcripts_to_process = []
            luna_was_mentioned_in_turn = False
            is_mention_trigger = False # Flag to know which path was taken

            if triggered_by_mention:
                # --- Case 1: Triggered by a specific "Luna" mention ---
                logger.info(f"Processing turn triggered by direct mention: {triggered_by_mention['original_display_name']}: {triggered_by_mention['text']}") # Use original_display_name
                transcripts_to_process = [triggered_by_mention]
                luna_was_mentioned_in_turn = True # Explicitly true
                is_mention_trigger = True

                # Clear the shared list immediately to discard other concurrent speech from this turn
                self.current_turn_transcripts.clear()
                logger.info("Cleared concurrent transcripts because turn was triggered by mention.")

            else:
                # --- Case 2: Triggered by group silence ---
                # Take a snapshot and clear the list *immediately* within the lock
                turn_transcripts = list(self.current_turn_transcripts) # Make a copy
                self.current_turn_transcripts.clear() # Clear the shared list

                if not turn_transcripts:
                    logger.warning("Processing turn (group silence), but no transcripts found after lock.")
                    return # Nothing to process

                # Sort transcripts by their original audio start time for correct order
                turn_transcripts.sort(key=lambda x: x.get("timestamp", 0))
                transcripts_to_process = turn_transcripts # Process all collected transcripts

                # Check if Luna was mentioned anywhere in this collected turn
                luna_was_mentioned_in_turn = any(t.get("mentioned_luna", False) for t in transcripts_to_process)

            # Reset activity timer and capture turn processing start time
            self.current_turn_start_monotonic_time = time.monotonic() # Store start time for this processing
            self.last_activity_time = self.current_turn_start_monotonic_time # Reset activity timer

            # Combine text for logging
            combined_text_log = " | ".join([f"{t['original_display_name']}: {t['text']}" for t in transcripts_to_process]) # Use original_display_name
            logger.info(f"Processing turn data (mention_trigger={is_mention_trigger}, mentioned_in_turn={luna_was_mentioned_in_turn}, count={len(transcripts_to_process)}): {combined_text_log}")

            # --- Add Processed Transcripts to Overall History ---
            # Add each transcript from the processed turn (either the single mention or the group)
            for transcript in transcripts_to_process:
                await self._add_to_history(
                    role="user",
                    content=f"{transcript['canonical_name']}: {transcript['text']}", # Use canonical name for history
                    user_id=transcript['user_id'],
                    audio_timestamp=transcript.get('timestamp', time.time()), # Use the correct keyword
                    channel_type="voice", # Log as voice
                    channel_id=str(self.voice_client.channel.id) if self.voice_client and self.voice_client.channel else None
                )

            # --- Call LLM Brain for Action Decision based on Voice Turn ---
            brain_action_taken = False
            # Only proceed if there are transcripts to analyze
            if transcripts_to_process:
                try:
                    # --- Preprocess Turn Text for Brain (Resolve Aliases) ---
                    turn_text_for_brain_raw = "\n".join([f"{t['canonical_name']} ({t['user_id']}): {t['text']}" for t in transcripts_to_process]) # Use canonical_name
                    processed_turn_text = turn_text_for_brain_raw # Start with raw text
                    # Apply similar alias resolution logic as in main.py, but on the combined turn text
                    potential_target_match_voice = re.search(r"(?:dm|tell|ask|kick|disconnect|call)\s+@?([\w\s]+)", turn_text_for_brain_raw, re.IGNORECASE | re.MULTILINE)
                    if potential_target_match_voice:
                        potential_alias_voice = potential_target_match_voice.group(1).strip().lower()
                        # Check if the potential alias is just a user ID first
                        if not re.fullmatch(r'\d{17,}', potential_alias_voice): # Don't resolve if it's likely an ID
                            logger.debug(f"Potential target alias found in voice turn text: '{potential_alias_voice}'")
                            resolved_main_name_voice = get_main_name_from_alias(potential_alias_voice)
                            if resolved_main_name_voice and resolved_main_name_voice.lower() != potential_alias_voice:
                                logger.info(f"Resolved alias '{potential_alias_voice}' to main name '{resolved_main_name_voice}' for voice brain input.")
                                # Replace first occurrence in the combined text (simple approach)
                                processed_turn_text = turn_text_for_brain_raw.replace(potential_target_match_voice.group(1), resolved_main_name_voice, 1)
                                logger.debug(f"Processed voice turn text for brain: '{processed_turn_text[:100]}...'")
                            else:
                                logger.debug(f"No specific main name found for potential voice alias '{potential_alias_voice}', using original turn text.")
                        else:
                            logger.debug(f"Potential voice alias '{potential_alias_voice}' looks like a user ID, skipping resolution.")

                    # Format context for the brain using the processed text
                    voice_channel_name = self.voice_client.channel.name if self.voice_client and self.voice_client.channel else "Unknown Voice Channel"
                    voice_channel_id = str(self.voice_client.channel.id) if self.voice_client and self.voice_client.channel else "Unknown"
                    turn_context_string = f"Conversation turn in voice channel '{voice_channel_name}' ({voice_channel_id}):\n{processed_turn_text}" # Use processed_turn_text

                    logger.info(f"Querying LLM Brain for action based on voice turn...")
                    brain_start_time = time.monotonic()
                    # --- Call Consolidated Brain Processing Function ---
                    brain_start_time = time.monotonic()
                    brain_action_details = await llm_brain.process_brain_request(
                        prompt=turn_context_string, # Pass the potentially modified context string
                        bot=self.bot,
                        sink=self, # Pass self (sink) for context
                        system_prompt=self.system_prompt, # Pass main system prompt
                        effective_text_channel=self.text_channel # Pass the default text channel for confirmations/errors
                    )
                    brain_end_time = time.monotonic()
                    brain_duration = brain_end_time - brain_start_time
                    await log_latency_to_discord(self.bot, f"LLM Brain Processing Duration: {brain_duration:.4f}s") # Log total brain processing time

                    # --- Handle Brain Action Result ---
                    if brain_action_details:
                         action_taken = brain_action_details.get('action', 'error')
                         logger.info(f"Brain Action Result: {action_taken}, Details: {brain_action_details}")

                         if action_taken in ['call_user', 'disconnect_user']:
                              if brain_action_details.get('success', False):
                                   logger.info(f"Brain successfully executed action: {action_taken}. Turn processing complete.")
                                   brain_action_taken = True # Mark action as successfully handled
                              else:
                                   logger.warning(f"Brain action '{action_taken}' failed execution. Raw: {brain_action_details.get('raw_decision')}")
                                   # Action was attempted but failed, still mark as 'handled' to prevent fallback response
                                   brain_action_taken = True
                         elif action_taken == 'dm_user':
                              # DM action needs to be executed here for voice commands
                              if brain_action_details.get('success', False):
                                   logger.info("Brain decided 'DM User' for voice command. Executing DM...")
                                   dm_success = await self._execute_dm_action(brain_action_details)
                                   if dm_success:
                                        logger.info("DM action executed successfully for voice command.")
                                        brain_action_taken = True
                                   else:
                                        logger.warning("DM action execution failed for voice command.")
                                        brain_action_taken = True # Still mark as handled to prevent fallback
                              else:
                                   logger.error(f"Brain action 'dm_user' indicated success=False unexpectedly. Raw: {brain_action_details.get('raw_decision')}")
                                   brain_action_taken = True # Mark as handled
                         elif action_taken == 'no_action':
                              logger.info("Brain decided 'No Action'.")
                              brain_action_taken = False # Explicitly false so conversational response proceeds
                         elif action_taken == 'error':
                              logger.error(f"Brain processing resulted in an error: {brain_action_details.get('details')}. Raw: {brain_action_details.get('raw_decision')}")
                              brain_action_taken = False # Proceed with standard response as fallback
                         else:
                              logger.warning(f"Unknown action returned from brain: {action_taken}. Proceeding with standard response.")
                              brain_action_taken = False # Proceed with standard response
                    else:
                         # Should not happen if process_brain_request always returns a dict, but handle defensively
                         logger.error("process_brain_request returned None unexpectedly.")
                         brain_action_taken = False # Proceed with standard response

                except Exception as brain_exec_err:
                     # Catch errors during the call to process_brain_request itself
                     logger.error(f"Error calling process_brain_request for voice turn: {brain_exec_err}", exc_info=True)
                     brain_action_taken = False # Proceed with standard response as fallback

            # --- If Brain Didn't Take Action, Proceed with Conversational Response ---
            if not brain_action_taken:
                logger.info("LLM Brain took no specific action for voice turn, proceeding with conversational response.")
                # Prepare data for the existing response logic (using last utterance)
                if not transcripts_to_process: # Should generally not happen if we got here
                     logger.warning("No transcripts available to determine last utterance for conversational response.")
                     return

                last_utterance_for_response = transcripts_to_process[-1]
                text_for_response = last_utterance_for_response['text']
                user_id_for_response = last_utterance_for_response['user_id']
                display_name_for_response = last_utterance_for_response['canonical_name'] # Use canonical_name

                # Call the existing response logic
                try:
                    # Extract latency timestamps from the last utterance
                    audio_detected_time = last_utterance_for_response.get("audio_detected_time")
                    transcription_end_time = last_utterance_for_response.get("transcription_end_time")

                    # Check if chat_session exists and has messages
                    if self.chat_session is None or not isinstance(self.chat_session, dict) or "messages" not in self.chat_session:
                        logger.warning("Voice chat session is missing or invalid. Creating a new one.")
                        # Create a new session
                        session_id = f"voice_channel_{int(time.time())}"
                        self.chat_session = {
                            "id": session_id,
                            "messages": []
                        }
                        # Add system prompt if available
                        if self.system_prompt:
                            self.chat_session["messages"].append({"role": "system", "content": self.system_prompt})
                            logger.info(f"Added system prompt to new chat session. Session now has {len(self.chat_session['messages'])} messages.")
                    else:
                        # Log the current state of the chat session
                        logger.info(f"Using existing voice chat session with {len(self.chat_session['messages'])} messages")
                        if len(self.chat_session["messages"]) > 0:
                            logger.info(f"First message in voice session: role={self.chat_session['messages'][0].get('role')}, content={self.chat_session['messages'][0].get('content')[:50]}...")
                        if len(self.chat_session["messages"]) > 1:
                            logger.info(f"Second message in voice session: role={self.chat_session['messages'][1].get('role')}, content={self.chat_session['messages'][1].get('content')[:50]}...")

                    await llm_response.process_user_message( # Ensure we call the function from the module
                        bot=self.bot,
                        text=text_for_response,
                        user_id=user_id_for_response,
                        conversation_history=self.conversation_history, # Still pass for logging/context
                        system_prompt=self.system_prompt, # Will be ignored if session provided
                        sink=self,
                        force_respond=luna_was_mentioned_in_turn, # Force if mentioned in turn
                        display_name=display_name_for_response,
                        text_channel=self.text_channel,
                        image_analysis_text=None, # No image analysis in voice turns currently
                        chat_session=self.chat_session, # Pass the stateful chat session
                        # Pass latency timestamps
                        audio_detected_time=audio_detected_time,
                        transcription_end_time=transcription_end_time
                    )
                except Exception as e:
                     logger.error(f"Error calling process_user_message from _process_collected_turn (fallback): {e}", exc_info=True)

            logger.info("Turn processing complete.")

    # --- Helper methods for user/member lookup ---

    async def _find_member_from_identifier(self, identifier: str) -> discord.Member | None:
        """Helper to find a guild member by ID, mention, or name/nick."""
        target_member = None
        user_id_to_fetch = None
        guild = self.voice_client.guild if self.voice_client else None
        if not guild:
            logger.error("_find_member_from_identifier: Cannot find member without guild context.")
            return None

        # 1. Try ID (mention or plain)
        mention_match = re.search(r"<@!?(\d+)>", identifier)
        plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", identifier)
        if mention_match:
            try: user_id_to_fetch = int(mention_match.group(1))
            except ValueError: pass
        elif plain_id_match:
            try: user_id_to_fetch = int(plain_id_match.group(1))
            except ValueError: pass

        if user_id_to_fetch:
            try:
                target_member = await guild.fetch_member(user_id_to_fetch)
            except (discord.NotFound, discord.HTTPException):
                target_member = None

        # 2. Fallback to name/nick
        if not target_member:
            name_to_search = re.sub(r'[<@!>#()\[\]]', '', identifier).strip()
            if name_to_search.lower().startswith("user "):
                name_to_search = name_to_search[5:].strip()
            if name_to_search:
                # Iterate through guild members for case-insensitive match
                for member in guild.members:
                    if member.name.lower() == name_to_search.lower() or \
                       (member.nick and member.nick.lower() == name_to_search.lower()):
                        target_member = member
                        break
        return target_member

    # Add this method to the DiscordSink class
    def get_display_name(self, user_id: int) -> str:
        """Gets the canonical main name for a user ID, falling back to User_ID."""
        main_name = get_main_name_by_id(user_id)
        if main_name:
            return main_name
        # Fallback if ID not in profiles
        logger.warning(f"User ID {user_id} not found in USER_PROFILES. Falling back to User_{user_id}.")
        return f"User_{user_id}"

    # _find_user_from_identifier function moved to utils.py to resolve circular import

# --- Standalone helper functions (if any) ---

def is_luna_mentioned(message, bot_user):
    # Check all mention types
    return any([
        bot_user.mentioned_in(message),
        re.search(rf'@?{re.escape(bot_user.name)}', message.content, re.I),
        'luna' in message.content.lower() and (
            message.reference and
            message.reference.resolved.author == bot_user
        )
    ])