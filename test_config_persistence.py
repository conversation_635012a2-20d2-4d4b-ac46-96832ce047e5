#!/usr/bin/env python3
"""
Test script to verify that volume settings persist to the config file.
"""

import sys
import os

# Add the current directory to the path so we can import the config
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_persistence():
    """Test that volume settings are properly saved to the config file."""
    print("Testing config file persistence...")
    
    try:
        from llm_response.config import (
            get_volume_multiplier_by_id,
            set_volume_multiplier_by_id,
            get_main_name_by_id,
            USER_PROFILES
        )
        
        # Find a test user (<PERSON>)
        test_user_id = None
        for user_id, profile in USER_PROFILES.items():
            if profile.get("main_name") == "Gavin":
                test_user_id = user_id
                break
        
        if test_user_id is None:
            print("❌ Could not find <PERSON> in USER_PROFILES")
            return False
        
        print(f"✅ Found test user: {get_main_name_by_id(test_user_id)} (ID: {test_user_id})")
        
        # Get current volume
        original_volume = get_volume_multiplier_by_id(test_user_id)
        print(f"📊 Current volume: {original_volume:.1f}x")
        
        # Set a new volume
        test_volume = 1.5 if original_volume != 1.5 else 1.8
        print(f"🔧 Setting volume to {test_volume:.1f}x...")
        
        success = set_volume_multiplier_by_id(test_user_id, test_volume)
        if not success:
            print("❌ Failed to set volume")
            return False
        
        # Verify the change in memory
        new_volume = get_volume_multiplier_by_id(test_user_id)
        if new_volume != test_volume:
            print(f"❌ Volume not updated in memory. Expected {test_volume}, got {new_volume}")
            return False
        
        print(f"✅ Volume updated in memory: {new_volume:.1f}x")
        
        # Check if the config file was actually modified
        config_file_path = os.path.join(os.path.dirname(__file__), "llm_response", "config.py")
        
        if not os.path.exists(config_file_path):
            print(f"❌ Config file not found at {config_file_path}")
            return False
        
        # Read the config file and check if our change is there
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # Look for our user ID and the new volume
        if f'"volume_multiplier": {test_volume}' in config_content:
            print("✅ Config file contains the new volume setting")
        else:
            print("❌ Config file does not contain the new volume setting")
            print("Config file content around USER_PROFILES:")
            lines = config_content.split('\n')
            in_profiles = False
            for i, line in enumerate(lines):
                if 'USER_PROFILES' in line:
                    in_profiles = True
                if in_profiles:
                    print(f"  {i+1}: {line}")
                if in_profiles and line.strip() == '}' and 'USER_PROFILES' not in line:
                    break
            return False
        
        # Reset to original volume
        print(f"🔄 Resetting volume to original value: {original_volume:.1f}x")
        set_volume_multiplier_by_id(test_user_id, original_volume)
        
        final_volume = get_volume_multiplier_by_id(test_user_id)
        if final_volume == original_volume:
            print(f"✅ Volume reset successfully: {final_volume:.1f}x")
        else:
            print(f"⚠️ Volume reset may have failed. Expected {original_volume}, got {final_volume}")
        
        print("✅ Config persistence test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_lookup():
    """Test that we can find users by their Discord mentions."""
    print("\nTesting user lookup...")
    
    try:
        from llm_response.config import USER_PROFILES, get_main_name_by_id
        
        print("Available users in config:")
        for user_id, profile in USER_PROFILES.items():
            name = profile.get("main_name", "Unknown")
            volume = profile.get("volume_multiplier", 1.0)
            print(f"  {name} (ID: {user_id}): {volume:.1f}x")
        
        # Test specific user lookup
        gavin_ids = []
        for user_id, profile in USER_PROFILES.items():
            if profile.get("main_name") == "Gavin":
                gavin_ids.append(user_id)
        
        if gavin_ids:
            print(f"✅ Found {len(gavin_ids)} user(s) named Gavin: {gavin_ids}")
        else:
            print("❌ No users named Gavin found")
        
        return True
        
    except Exception as e:
        print(f"❌ User lookup test failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Config Persistence Test ===")
    
    success1 = test_user_lookup()
    success2 = test_config_persistence()
    
    if success1 and success2:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
